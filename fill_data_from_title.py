import pandas as pd
import logging
from datetime import datetime
from db import Database
import os
import argparse

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/fill_data_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_asset_details(title: str) -> list:
    """Get asset details from database based on title"""
    query = """
    SELECT 
        asset_id,
        content_id,
        is_asset_publishable,
        is_content_publishable
    FROM videocentral.asset_metadata
    WHERE asset_title = %s
        AND avail_start_date IS NOT NULL;
    """
    
    try:
        db = Database()
        results = db.execute(query, (title,))
        db.close()
        return results
    except Exception as e:
        logger.error(f"Error executing query: {str(e)}")
        return []

def process_excel_file(excel_file: str, sheet_name: str = None):
    """Process Excel file and update asset details for each row"""
    try:
        # Read existing Excel file
        if not os.path.exists(excel_file):
            logger.error(f"Excel file not found: {excel_file}")
            return

        # Read Excel file with specified sheet name
        if sheet_name:
            df = pd.read_excel(excel_file, sheet_name=sheet_name)
            logger.info(f"Loaded Excel file: {excel_file}, Sheet: {sheet_name}")
        else:
            df = pd.read_excel(excel_file)
            logger.info(f"Loaded Excel file: {excel_file} (default sheet)")

        # Ensure required columns exist
        required_columns = ['Titles', 'asset_id', 'content_id']
        for col in required_columns:
            if col not in df.columns:
                df[col] = None
                logger.info(f"Added column: {col}")

        # Process each row
        new_rows = []
        for index, row in df.iterrows():
            title = row['Titles']
            if (title is None or title == '' or str(title).lower() in ['nan', 'none', 'null'] or not str(title).strip()):
                logger.warning(f"Skipping row {index}: No title found")
                continue

            logger.info(f"Processing title: {title}")
            results = get_asset_details(title)
            
            if not results:
                logger.warning(f"No results found for title: {title}")
                continue

            # Store the Drive Link from current row
            # current_drive_link = row['Drive Link']

            if len(results) == 1:
                # Single entry - update current row
                result = results[0]
                df.at[index, 'asset_id'] = result['asset_id']
                df.at[index, 'content_id'] = result['content_id']
                logger.info(f"Updated row {index} for asset_id: {result['asset_id']}")

            else:
                # Multiple entries - check publishable status
                publishable_entries = [
                    r for r in results 
                    if r['is_asset_publishable'] == 'YES' and r['is_content_publishable'] == 'YES'
                ]
                
                if publishable_entries:
                    # Update current row with first publishable entry
                    first_entry = publishable_entries[0]
                    df.at[index, 'asset_id'] = first_entry['asset_id']
                    df.at[index, 'content_id'] = first_entry['content_id']
                    logger.info(f"Updated row {index} with first publishable entry: {first_entry['asset_id']}")

                    # Create new rows for additional publishable entries
                    for entry in publishable_entries[1:]:
                        new_row = row.copy()
                        new_row['asset_id'] = entry['asset_id']
                        new_row['content_id'] = entry['content_id']
                        # new_row['Drive Link'] = current_drive_link
                        new_rows.append(new_row)
                        logger.info(f"Created new row for asset_id: {entry['asset_id']}")
                else:
                    logger.warning(f"No publishable entries found for title: {title}")

        # Add new rows if any
        if new_rows:
            new_df = pd.DataFrame(new_rows)
            df = pd.concat([df, new_df], ignore_index=True)
            logger.info(f"Added {len(new_rows)} new rows")

        # Save to Excel
        df.to_excel(excel_file, index=False)
        logger.info(f"Saved updated data to: {excel_file}")

    except Exception as e:
        logger.error(f"Error processing Excel file: {str(e)}")

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Fill asset details from title in Excel file')
    parser.add_argument('-f', '--file', default='redelivery_list.xlsx',
                      help='Excel file to process (default: redelivery_list.xlsx)')
    parser.add_argument('-s', '--sheet', default=None,
                      help='Sheet name to process (default: first sheet)')

    args = parser.parse_args()

    if args.sheet:
        logger.info(f"Starting process with file: {args.file}, Sheet: {args.sheet}")
    else:
        logger.info(f"Starting process with file: {args.file} (default sheet)")

    process_excel_file(args.file, args.sheet)
    logger.info("Process completed")

if __name__ == "__main__":
    main()

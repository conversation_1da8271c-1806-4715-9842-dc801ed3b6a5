2025-07-13 16:17:11,117 - INFO - [DB] Fetching blocked territories for content_id=6683236
2025-07-13 16:17:11,278 - INFO - 🤜 Removed blocked territories from list: ['GB', 'UM', 'US']
2025-07-13 16:17:11,278 - INFO - [IN] [['AVAILS']] Initiating re-delivery for content_id: 6683236, asset_id: 1047740
2025-07-13 16:17:11,615 - INFO - 🫩 Successfully initiated redelivery for content_id: 6683236
2025-07-13 16:17:11,615 - INFO - 🕑 Waiting 10 seconds before status check...
2025-07-13 16:17:21,620 - INFO - 🕑 Checking status for tasks: ['AVAILS']
2025-07-13 16:17:21,621 - INFO - [DB] Starting check_delivery_status with params: task_types=['AVAILS'], content_id=6683236, territory=IN
2025-07-13 16:17:21,888 - INFO - [DB] Mapped task_type AVAILS to db_task_types: ['AVAILS']
2025-07-13 16:17:21,889 - INFO - [DB] Executing query with params ( content_id: 6683236, task_type: AVAILS, territory: IN )
2025-07-13 16:17:22,064 - INFO - [DB] Query executed successfully with result: {'status': 'COMPLETED', 'updated_at': datetime.datetime(2025, 1, 31, 10, 21, 43, 663817)}
2025-07-13 16:17:22,065 - INFO - [DB] Found status: COMPLETED (updated at: 2025-01-31 10:21:43.663817) for content_id: 6683236, task_type: AVAILS, territory: IN
2025-07-13 16:17:22,065 - INFO - [DB] Result fetched for delivery status: dict_values(['COMPLETED', datetime.datetime(2025, 1, 31, 10, 21, 43, 663817)])
2025-07-13 16:17:22,065 - INFO - ✨ Status for AVAILS: COMPLETED
2025-07-13 16:17:22,065 - INFO - 🟢 Final statuses: {'AVAILS': 'COMPLETED'}
2025-07-13 16:17:22,066 - INFO - ✅ Successfully initiated redelivery for Rishya Singer in IN
2025-07-13 16:17:22,067 - INFO - [DB] Fetching blocked territories for content_id=6733310
2025-07-13 16:17:22,844 - INFO - 🤜 Removed blocked territories from list: ['BD']
2025-07-13 16:17:22,844 - INFO - [GB] [['AVAILS']] Initiating re-delivery for content_id: 6733310, asset_id: 1055225
2025-07-13 16:17:23,523 - INFO - 🫩 Successfully initiated redelivery for content_id: 6733310
2025-07-13 16:17:23,524 - INFO - 🕑 Waiting 10 seconds before status check...
2025-07-13 16:17:33,531 - INFO - 🕑 Checking status for tasks: ['AVAILS']
2025-07-13 16:17:33,532 - INFO - [DB] Starting check_delivery_status with params: task_types=['AVAILS'], content_id=6733310, territory=GB
2025-07-13 16:17:33,609 - INFO - [DB] Mapped task_type AVAILS to db_task_types: ['AVAILS']
2025-07-13 16:17:33,609 - INFO - [DB] Executing query with params ( content_id: 6733310, task_type: AVAILS, territory: GB )
2025-07-13 16:17:33,691 - INFO - [DB] Query executed successfully with result: None
2025-07-13 16:17:33,692 - INFO - [DB] No status found for content_id: 6733310, task_type: AVAILS, territory: GB
2025-07-13 16:17:33,692 - INFO - 🔴 No status found for AVAILS
2025-07-13 16:17:33,692 - INFO - 🟢 Final statuses: {'AVAILS': None}
2025-07-13 16:17:33,692 - INFO - ❌ Failed to initiate redelivery for Secret Of Bedrooms in GB
2025-07-13 16:17:33,693 - INFO - [IN] [['AVAILS']] Initiating re-delivery for content_id: 6733310, asset_id: 1055225
2025-07-13 16:17:33,818 - INFO - 🫩 Successfully initiated redelivery for content_id: 6733310
2025-07-13 16:17:33,819 - INFO - 🕑 Waiting 10 seconds before status check...
2025-07-13 16:17:43,821 - INFO - 🕑 Checking status for tasks: ['AVAILS']
2025-07-13 16:17:43,822 - INFO - [DB] Starting check_delivery_status with params: task_types=['AVAILS'], content_id=6733310, territory=IN
2025-07-13 16:17:43,904 - INFO - [DB] Mapped task_type AVAILS to db_task_types: ['AVAILS']
2025-07-13 16:17:43,905 - INFO - [DB] Executing query with params ( content_id: 6733310, task_type: AVAILS, territory: IN )
2025-07-13 16:17:43,971 - INFO - [DB] Query executed successfully with result: {'status': 'COMPLETED', 'updated_at': datetime.datetime(2021, 6, 4, 11, 16, 58, 221144)}
2025-07-13 16:17:43,972 - INFO - [DB] Found status: COMPLETED (updated at: 2021-06-04 11:16:58.221144) for content_id: 6733310, task_type: AVAILS, territory: IN
2025-07-13 16:17:43,972 - INFO - [DB] Result fetched for delivery status: dict_values(['COMPLETED', datetime.datetime(2021, 6, 4, 11, 16, 58, 221144)])
2025-07-13 16:17:43,972 - INFO - ✨ Status for AVAILS: COMPLETED
2025-07-13 16:17:43,972 - INFO - 🟢 Final statuses: {'AVAILS': 'COMPLETED'}
2025-07-13 16:17:43,972 - INFO - ✅ Successfully initiated redelivery for Secret Of Bedrooms in IN
2025-07-13 16:17:43,972 - INFO - [US] [['AVAILS']] Initiating re-delivery for content_id: 6733310, asset_id: 1055225
2025-07-13 16:17:44,103 - INFO - 🫩 Successfully initiated redelivery for content_id: 6733310
2025-07-13 16:17:44,103 - INFO - 🕑 Waiting 10 seconds before status check...
2025-07-13 16:17:54,109 - INFO - 🕑 Checking status for tasks: ['AVAILS']
2025-07-13 16:17:54,110 - INFO - [DB] Starting check_delivery_status with params: task_types=['AVAILS'], content_id=6733310, territory=US
2025-07-13 16:17:54,232 - INFO - [DB] Mapped task_type AVAILS to db_task_types: ['AVAILS']
2025-07-13 16:17:54,232 - INFO - [DB] Executing query with params ( content_id: 6733310, task_type: AVAILS, territory: US )
2025-07-13 16:17:54,316 - INFO - [DB] Query executed successfully with result: None
2025-07-13 16:17:54,316 - INFO - [DB] No status found for content_id: 6733310, task_type: AVAILS, territory: US
2025-07-13 16:17:54,316 - INFO - 🔴 No status found for AVAILS
2025-07-13 16:17:54,317 - INFO - 🟢 Final statuses: {'AVAILS': None}
2025-07-13 16:17:54,317 - INFO - ❌ Failed to initiate redelivery for Secret Of Bedrooms in US
2025-07-13 16:17:54,318 - INFO - 


2025-07-13 16:17:54,318 - INFO - === Processing Summary ===
2025-07-13 16:17:54,319 - INFO - Total Assets: 2
2025-07-13 16:17:54,319 - INFO - Total Success: 2
2025-07-13 16:17:54,319 - INFO - Total Failed: 2
2025-07-13 16:17:54,319 - INFO - Total Skipped: 0
2025-07-13 16:17:54,320 - INFO - 
--- Failed Assets ---
2025-07-13 16:17:54,320 - INFO - Row: 1
2025-07-13 16:17:54,320 - INFO - Title: Secret Of Bedrooms
2025-07-13 16:17:54,320 - INFO - Territory: GB
2025-07-13 16:17:54,320 - INFO - Asset ID: 1055225
2025-07-13 16:17:54,320 - INFO - Content ID: 6733310
2025-07-13 16:17:54,320 - INFO - Error: Failed to initiate redelivery
2025-07-13 16:17:54,320 - INFO - ---
2025-07-13 16:17:54,321 - INFO - Row: 1
2025-07-13 16:17:54,321 - INFO - Title: Secret Of Bedrooms
2025-07-13 16:17:54,321 - INFO - Territory: US
2025-07-13 16:17:54,321 - INFO - Asset ID: 1055225
2025-07-13 16:17:54,321 - INFO - Content ID: 6733310
2025-07-13 16:17:54,321 - INFO - Error: Failed to initiate redelivery
2025-07-13 16:17:54,321 - INFO - ---
2025-07-13 16:17:54,321 - INFO - 


2025-07-13 16:17:54,321 - INFO - ==============================
2025-07-13 16:17:54,321 - INFO - Total Duration: 0:00:43.267513
2025-07-13 16:17:54,321 - INFO - ==============================



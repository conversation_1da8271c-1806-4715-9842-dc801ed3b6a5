import argparse
from datetime import datetime
import logging
import os
import tempfile

import pandas as pd

from db import Database
from spreadsheet_reader import SpreadsheetReader

os.makedirs('poster_verify', exist_ok=True)

default_source_file = 'ENPoster_Merge_14.xlsx'
default_output_file = 'ENPoster_Validation_14.xlsx'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/asset_delivery_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class PosterVerify:
    def __init__(self, excel_file: str = default_source_file, sheet_name: str = None, output_excel_path: str = default_output_file):
        self.db = Database()
        self.spreadsheet = SpreadsheetReader(excel_file, sheet_name)
        self.temp_dir = tempfile.mkdtemp()
        self.excel_file = excel_file
        self.output_excel_file = output_excel_path
        self.stats = {
            'total_assets': 0,
            'start_time': None,
            'end_time': None
        }
        
    def validate_en_posters_from_excel(self):
        self.stats['start_time'] = datetime.now()
        db = Database()

        try:
            df = pd.read_excel(self.excel_file)
            required_columns = {"Asset ID", "Content ID", "Asset Title"}
            if not required_columns.issubset(df.columns):
                raise ValueError(f"Input file must contain columns: {required_columns}")

            self.stats['total_assets'] = len(df)
            results = []
            for _, row in df.iterrows():
                asset_id = row["Asset ID"]
                content_id = row["Content ID"]
                asset_title = row["Asset Title"]
                
                row_result = {
                    "ASSET_ID": asset_id,
                    "CONTENT_ID": content_id,
                    "ASSET_TITLE": asset_title
                }

                for territory in ["IN", "GB", "US"]:
                    status = db.check_delivery_status("MOVIE_POSTER_IMAGE", content_id, territory)
                    row_result[f"STATUS_{territory}"] = status

                results.append(row_result)

            output_df = pd.DataFrame(results)
            output_df.to_excel(self.output_excel_file, index=False)
            print(f"Validation completed. Output written to {self.output_excel_file}")

        except Exception as e:
            print(f"Error during validation: {e}")
        finally:
            self._print_summary()
            self._cleanup()
            
    def _print_summary(self):
        """Print summary statistics and delivery details"""
        self.stats['end_time'] = datetime.now()
        duration = self.stats['end_time'] - self.stats['start_time']
        
        logger.info("\n\n")
        logger.info("=== Processing Summary ===")
        logger.info(f"Total Assets: {self.stats['total_assets']}")        
        logger.info(f"Total Duration: {duration}")
        logger.info("==============================\n\n")

    def _cleanup(self):
        """Clean up temporary files and close connections"""
        try:
            self.db.close()
            
        except Exception as e:
            logger.warning(f"Error during cleanup: {str(e)}")
            
def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Process asset deliveries from Excel file')
    parser.add_argument('-f', '--file', 
                        default=default_source_file,
                        help=f'Excel file to process (default: images.xlsx)')
    parser.add_argument('-s', '--sheet',
                        default="Sheet1",
                        help='Specify sheet name if using a different sheet')
    args = parser.parse_args()

    # Create validator with specified file
    validator = PosterVerify(args.file, sheet_name=args.sheet)
    validator.validate_en_posters_from_excel()

if __name__ == "__main__":
    main() 

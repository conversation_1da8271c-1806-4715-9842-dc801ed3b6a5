from pathlib import Path

# Excel Configuration
DEFAULT_EXCEL_FILE = "redelivery_list.xlsx"
REQUIRED_EXCEL_COLUMNS = ['asset_id', 'content_id', 'Drive Link']
POSTER_REQUIRED_COLUMNS = ['asset_id', 'content_id', 'Drive Link']
REMOVAL_REQUIRED_COLUMNS = ['asset_id', 'content_id']
ONLY_POSTER_REQUIRED_COLUMNS = [ 'Asset ID', 'Content ID', 'Thumbnails Ready', 'Thumbnail Drive' ]

# API Configuration
API_BASE_URL = "http://amazonfeeds-web.erosnow.com/aspera/delivery"
API_TIMEOUT = 30  # seconds

# Database Configuration
DB_CONFIG = {
    "host": "***********",
    "user": "eros",
    "password": "er0s1ntl",
    "port": 3306
}

# Google Cloud Storage Configuration
GCS_BUCKET_NAME = "erosnow-images-001"
GCS_CREDENTIALS_FILE = "gcs_credentials.json"

# Download Configuration
DOWNLOAD_DIR = "downloads"

# Image Configuration
ALLOWED_IMAGE_TYPES = ['.jpg', '.jpeg', '.png']

# Territory Configuration
TERRITORIES = ['IN', 'GB', 'US']

# Content Type IDs
CONTENT_TYPE_IDS = {
    'HERO_ART': 40,
    'COVER_ART': 120,
    'BOX_ART': 53,
    'POSTER_ART': 96
}

# TASK_TYPE_IDS
TASK_TYPE_IDS = {
    'MOVIE_HERO_IMAGE': 40,
    'MOVIE_COVER_IMAGE': 120,
    'MOVIE_IMAGE': 53,
    'MOVIE_POSTER_IMAGE': 96
}

# Retry Configuration
MAX_RETRIES = 3

# Task Types Configuration
TASK_TYPES = {
    'MOVIE_IMAGE': 'MOVIE_IMAGE',
    'MOVIE_COVER_IMAGE': 'MOVIE_COVER_IMAGE',
    'MOVIE_HERO_IMAGE': 'MOVIE_HERO_IMAGE',
    'MOVIE_POSTER_IMAGE': 'MOVIE_POSTER_IMAGE',
    'MEC_MOVIE': 'MEC_MOVIE'
}

IMAGE_TASK_TYPES = [ 'MOVIE_IMAGE', 'MOVIE_COVER_IMAGE', 'MOVIE_HERO_IMAGE', 'MOVIE_POSTER_IMAGE' ]

# Database Status Configuration
DB_STATUS = {
    "COMPLETED": "COMPLETED",
    "FILE_DELIVERY_IN_PROGRESS": "FILE_DELIVERY_IN_PROGRESS",
    "FAILED": "FAILED",
}
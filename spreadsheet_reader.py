import pandas as pd
from config import REQUIRED_EXCEL_COLUMNS, POSTER_REQUIRED_COLUMNS, DEFAULT_EXCEL_FILE, ONLY_POSTER_REQUIRED_COLUMNS, REMOVAL_REQUIRED_COLUMNS
import logging

logger = logging.getLogger(__name__)

class SpreadsheetReader:
    def __init__(self, excel_file: str = DEFAULT_EXCEL_FILE, sheet_name=None, image_type=None):
        self.excel_file = excel_file
        self.sheet_name = sheet_name
        self.image_type = image_type
        
    def read_sheet_ac(self):
        """
        Read the Excel file and return a DataFrame with standardized column names
        """
        try:
            if self.sheet_name:
                    df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
            else:
                df = pd.read_excel(self.excel_file)
                
            df.columns = df.columns.str.strip().str.lower().str.replace(' ', '_')

            # Create a mapping of known variants to desired names
            rename_map = {
                'asset_id': 'asset_id',
                'asset id': 'asset_id',
                'assetid': 'asset_id',
                
                'content_id': 'content_id',
                'content id': 'content_id',
                'contentid': 'content_id'
            }
            
            # Apply mapping
            df.columns = [rename_map.get(col, col) for col in df.columns]
            
            return df
        except Exception as e:
            raise Exception(f"Error reading Excel file {self.excel_file}: {str(e)}")
        
    def get_column_mapping(self):
        """
        Return column mapping based on image type
        """
        if self.image_type == 'poster':
            # Column mapping for poster image sheet
            return {
                'Asset ID': 'asset_id',
                'Content ID': 'content_id',
                'Thumbnail Drive': 'Drive Link'
            }
        # Default column mapping (no changes needed)
        return {}

    def read_sheet(self):
        """
        Read the Excel file and return a DataFrame with standardized column names
        """
        try:
            # Read Excel with specified sheet name if provided
            if self.sheet_name:
                df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
            else:
                df = pd.read_excel(self.excel_file)
            
            # Get column mapping for the current image type
            column_mapping = self.get_column_mapping()
            
            # Apply column mapping if needed
            if column_mapping:
                # Rename columns according to mapping
                df = df.rename(columns=column_mapping)
            
            # Get the appropriate required columns based on image type
            required_columns = POSTER_REQUIRED_COLUMNS if self.image_type == 'poster' else REQUIRED_EXCEL_COLUMNS
            
            # Validate required columns
            missing_columns = set(required_columns) - set(df.columns)
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            return df
        except Exception as e:
            raise Exception(f"Error reading Excel file {self.excel_file}: {str(e)}")

    def get_drive_link(self, asset_id: str) -> str:
        """
        Get the drive link for a specific asset ID
        """
        try:
            # Read the Excel file
            df = self.read_sheet()
            
            # Find the row with the matching asset_id
            matching_rows = df[df['asset_id'] == asset_id]
            if matching_rows.empty:
                logger.error(f"No matching row found for asset_id: {asset_id}")
                return None
            
            # Get the drive link from the first matching row
            drive_link = matching_rows.iloc[0]['Drive Link']
            return drive_link
        except Exception as e:
            logger.error(f"Error getting drive link for asset_id {asset_id}: {str(e)}")
            return None
        
    def read_only_poster(self):
        try:
            # Read Excel with specified sheet name if provided
            if self.sheet_name:
                df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
            else:
                df = pd.read_excel(self.excel_file)
            
            # Get column mapping for the current image type
            column_mapping = self.get_column_mapping()
            
            # Apply column mapping if needed
            if column_mapping:
                # Rename columns according to mapping
                df = df.rename(columns=column_mapping)
            
            # Validate required columns
            missing_columns = set(ONLY_POSTER_REQUIRED_COLUMNS) - set(df.columns)
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            return df
        except Exception as e:
            raise Exception(f"Error reading Excel file {self.excel_file}: {str(e)}")
    
    def read_all_images_sheet(self):
        df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
        df = df.rename(columns={
            'ASSET ID': 'asset_id',
            'CONTENT ID': 'content_id',
            '1920x1080': 'hero_cover',
            '1920x2560': 'box',
            '2000x3000': 'poster',
            'ASSET TITLE': 'asset_title'
        })
        return df
    
    def read_excel_for_removal(self):
        df = pd.read_excel(self.excel_file, sheet_name=self.sheet_name)
        df.columns = df.columns.str.strip().str.lower().str.replace(' ', '_')

        rename_map = {
            'asset_id': 'asset_id',
            'asset id': 'asset_id',
            'assetid': 'asset_id',
            
            'content_id': 'content_id',
            'content id': 'content_id',
            'contentid': 'content_id'
        }
        df.columns = [rename_map.get(col, col) for col in df.columns]
        
        missing_columns = set(REMOVAL_REQUIRED_COLUMNS) - set(df.columns)
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        return df
import requests
import logging
import time
from typing import Optional, List, Dict, Union, Set
from datetime import datetime, timedelta

from config import API_BASE_URL, API_TIMEOUT
from db import Database

logger = logging.getLogger(__name__)

# Define valid task types
VALID_TASK_TYPES = {
    'MOVIE_POSTER_IMAGE': 'MOVIE_POSTER_IMAGE',
    'MOVIE_HERO_IMAGE': 'MOVIE_HERO_IMAGE',
    'MOVIE_COVER_IMAGE': 'MOVIE_COVER_IMAGE',
    'MOVIE_IMAGE': 'MOVIE_IMAGE',
    'MEC_TITLE': 'MEC_TITLE',
}

# Define image task types for IMAGES replacement
IMAGE_TASK_TYPES = [
    'MOVIE_IMAGE',
    'MOVIE_COVER_IMAGE',
    'MOVIE_HERO_IMAGE',
    'MOVIE_POSTER_IMAGE'
]

class DeliveryService:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = API_BASE_URL
        self.db = Database()

    def _expand_task_types(self, task_types: List[str]) -> List[str]:
        """Expand IMAGES task type into specific image types"""
        expanded_types = []
        for t_type in task_types:
            if t_type == 'IMAGES':
                expanded_types.extend(IMAGE_TASK_TYPES)
                logger.info(f"Expanded 'IMAGES' into specific types: {IMAGE_TASK_TYPES}")
            elif t_type == 'MEC_TITLE':
                expanded_types.append('MEC_MOVIE')
            else:
                expanded_types.append(t_type)
        return expanded_types

    def initiate_redelivery(self, content_id: str, asset_id: str, territory: str, task_type: Union[str, List[str]], avail_end_date: str = '2049-12-31') -> Union[bool, Dict[str, str]]:
        """Initiate redelivery for a content asset"""
        try:
            # Convert single task type to list for uniform processing
            task_types = [task_type] if isinstance(task_type, str) else task_type
            
            payload = {
                "content_id__in": [content_id],
                "asset_id__in": [asset_id],
                "territories": [territory],
                "avail_end_date": avail_end_date,
                "asset_type__in": [],
                "task_type": task_types
            }
            
            logger.info(f"[{territory}] [{task_types}] Initiating re-delivery for content_id: {content_id}, asset_id: {asset_id}")
            
            response = self.session.post(
                self.base_url,
                json=payload,
                timeout=API_TIMEOUT
            )
            
            if response.status_code == 200:
                logger.info(f"🫩 Successfully initiated redelivery for content_id: {content_id}")

                # Determine wait time: 15 seconds for images, 10 seconds for others
                wait_time = 15 if 'IMAGES' in task_types else 10
                final_statuses = {}

                # Expand IMAGES into specific types for status checking
                pending_tasks = set(self._expand_task_types(task_types))

                if 'IMAGES' in task_types:
                    # Get available images from db
                    available_images = self.db.get_available_images(asset_id)

                    original_tasks = pending_tasks.copy()
                    pending_tasks = pending_tasks.intersection(set(available_images))
                    skipped = original_tasks - pending_tasks
                    if skipped:
                        logger.info(f"⏭️ Skipping unavailable tasks for {asset_id}: {skipped}")

                    logger.info(f"✨ Tracking status for tasks: {sorted(pending_tasks)}")

                # Wait before status check
                logger.info(f"🕑 Waiting {wait_time} seconds before status check...")
                time.sleep(wait_time)

                # Single status check (no retry loop)
                logger.info(f"🕑 Checking status for tasks: {sorted(pending_tasks)}")
                statuses = self.db.check_delivery_status(list(pending_tasks), content_id, territory)

                # Check each task
                for t_type in pending_tasks:
                    status = statuses.get(t_type)

                    if not status:
                        logger.info(f"🔴 No status found for {t_type}")
                        final_statuses[t_type] = None
                        continue

                    logger.info(f"✨ Status for {t_type}: {status}")
                    final_statuses[t_type] = status

                logger.info(f"🟢 Final statuses: {final_statuses}")

                # Return results based on task type
                if isinstance(task_type, str):
                    # If original task was IMAGES, check if all image types completed successfully
                    if task_type == 'IMAGES':
                        image_statuses = [final_statuses.get(t) == 'COMPLETED' for t in IMAGE_TASK_TYPES if t in final_statuses]
                        return all(image_statuses) if image_statuses else False
                    elif task_type == 'MEC_TITLE':
                        return final_statuses.get('MEC_MOVIE') == 'COMPLETED'
                    return final_statuses.get(task_type) == 'COMPLETED'
                return final_statuses
                
            elif response.status_code == 503:
                logger.warning(f"🔴 Service temporarily unavailable (503) for content_id: {content_id}")
                if isinstance(task_type, str):
                    return False
                return {t: None for t in task_types}
            else:
                logger.error(f"🔴 Failed to initiate redelivery. Status code: {response.status_code}")
                logger.error(f"Response: {response.text}")
                if isinstance(task_type, str):
                    return False
                return {t: None for t in task_types}
                
        except requests.exceptions.RequestException as e:
            logger.error(f"🔴 Error making API request: {str(e)}")
            if isinstance(task_type, str):
                return False
            return {t: None for t in task_types}
        except Exception as e:
            logger.error(f"🔴 Unexpected error in initiate_redelivery: {str(e)}")
            if isinstance(task_type, str):
                return False
            return {t: None for t in task_types}

    def close(self):
        """Close the requests session and database connection"""
        self.session.close()
        self.db.close() 
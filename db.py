import mysql.connector
from mysql.connector import <PERSON>rror
from config import DB_CONFIG, DB_STATUS, TASK_TYPE_IDS
from typing import Optional, List, Dict, Union
import logging
from validation import validate

logger = logging.getLogger(__name__)


class Database:
    def __init__(self):
        try:
            self.conn = mysql.connector.connect(**DB_CONFIG)
            self.cursor = self.conn.cursor(dictionary=True)
        except Error as e:
            print(f"Error connecting to MySQL database: {e}")
            raise

    def execute(self, query, params=None):
        try:                
            self.cursor.execute(query, params)
            return self.cursor.fetchall()
        except Error as e:
            print(f"Error executing query: {e}")
            raise

    def get_revision_number(
        self, asset_id: str, territory: str, task_type: str = "MOVIE_IMAGE"
    ) -> Optional[int]:
        """Get the current revision number for an asset in a territory"""
        try:
            # Validate task_type and territory
            is_valid, error_message = validate(task_type=task_type, territory=territory)
            if not is_valid:
                logger.error(f"[DB] Validation error: {error_message}")
                return None

            query = """
                SELECT 
                    fd.revision_number
                FROM
                    videocentral.file_delivery fd
                        INNER JOIN
                    videocentral.asset_metadata am ON am.id = fd.asset_metadata_id
                        INNER JOIN
                    gobe_catalog.gobe_ContentTypes ct ON ct.content_type_id = am.content_type_id
                WHERE
                    am.asset_id = %s
                    AND fd.task_type = %s
                    AND fd.territory = %s
            """
            self.cursor.execute(query, (asset_id, task_type, territory))
            result = self.cursor.fetchone()

            # Check if we got a result and if it contains a non-None revision number
            if result and result.get("revision_number") is not None:
                return result.get("revision_number")

            # If no result or NULL value, log it for debugging
            logger.info(
                f"[DB] No revision number found for asset {asset_id} in territory {territory} for task type {task_type}"
            )
            return None

        except Exception as e:
            logger.error(f"[DB] Error getting revision number: {e}")
            return None

    def check_delivery_status(
        self, task_type: Union[str, List[str]], content_id: str, territory: str
    ) -> Union[Optional[str], Dict[str, Optional[str]]]:
        """Check delivery status for one or more task types
        
        Args:
            task_type: Either a single task type string or a list of task types
            content_id: The content ID to check
            territory: The territory to check
            
        Returns:
            If task_type is a string: Returns the status string or None
            If task_type is a list: Returns a dict mapping task types to their statuses
        """
        try:
            # Convert single task type to list for uniform processing
            task_types = [task_type] if isinstance(task_type, str) else task_type.copy()
            logger.info(f"[DB] Starting check_delivery_status with params: task_types={task_types}, content_id={content_id}, territory={territory}")
            
            # Replace 'IMAGES' with specific image task types if present
            if 'IMAGES' in task_types:
                task_types.remove('IMAGES')
                task_types.extend(['MOVIE_IMAGE', 'MOVIE_COVER_IMAGE', 'MOVIE_HERO_IMAGE', 'MOVIE_POSTER_IMAGE'])
                logger.info(f"[DB] Replaced 'IMAGES' with specific image task types. Updated task_types: {task_types}")
            
            if 'MEC_TITLE' in task_types:
                task_types.remove('MEC_TITLE')
                task_types.append('MEC_MOVIE')
                logger.info(f"[DB] Replaced 'MEC_TITLE' with specific image task types. Updated task_types: {task_types}")
                
            # Validate all task types
            for t_type in task_types:
                is_valid, error_message = validate(task_type=t_type, territory=territory)
                if not is_valid:
                    logger.error(f"[DB] Validation error for task_type {t_type}: {error_message}")
                    if isinstance(task_type, str):
                        return None
                    else:
                        return {t: None for t in task_types}

            # Map API task types to database task types
            task_type_mapping = {
                "AVAILS": ["AVAILS"],
                "MEC_TITLE": ["MEC_MOVIE"],
                "MOVIE_COVER_IMAGE": ["MOVIE_COVER_IMAGE"],
                "MOVIE_HERO_IMAGE": ["MOVIE_HERO_IMAGE"],
                "MOVIE_IMAGE": ["MOVIE_IMAGE"],
                "MOVIE_POSTER_IMAGE": ["MOVIE_POSTER_IMAGE"],
            }

            # Check if the connection is still alive
            if not self.conn.is_connected():
                logger.error("[DB] Database connection is not active, attempting to reconnect")
                self.conn.reconnect()

            # Dictionary to store results
            status_results = {}
            
            # Process each task type
            for api_task_type in task_types:
                # Get the database task types for this API task type
                db_task_types = task_type_mapping.get(api_task_type, [api_task_type])
                logger.info(f"[DB] Mapped task_type {api_task_type} to db_task_types: {db_task_types}")

                # Check status for each database task type
                for db_task_type in db_task_types:
                    query = """
                        SELECT SQL_NO_CACHE fd.status, fd.updated_at
                        FROM videocentral.file_delivery fd
                        INNER JOIN videocentral.asset_metadata am ON am.id = fd.asset_metadata_id
                        WHERE am.content_id = %s
                        AND fd.task_type = %s
                        AND fd.territory = %s
                        ORDER BY fd.updated_at DESC
                    """
                    logger.info(f"[DB] Executing query with params ( content_id: {content_id}, task_type: {db_task_type}, territory: {territory} )")
                    self.cursor.execute(query, (content_id, db_task_type, territory))
                    result = self.cursor.fetchone()
                    logger.info(f"[DB] Query executed successfully with result: {result}")
                    if result:
                        logger.info(f"[DB] Found status: {result['status']} (updated at: {result['updated_at']}) for content_id: {content_id}, task_type: {db_task_type}, territory: {territory}")
                        logger.info(f"[DB] Result fetched for delivery status: {result.values()}")
                        status_results[api_task_type] = result['status']
                        break  # Found status for this task type, move to next
                    else:
                        logger.info(f"[DB] No status found for content_id: {content_id}, task_type: {db_task_type}, territory: {territory}")
                        status_results[api_task_type] = None

            # Return single status or dictionary based on input type
            if isinstance(task_type, str):
                return status_results.get(task_type)
            return status_results

        except Exception as e:
            logger.error(f"[DB] Error checking delivery status: {str(e)}")
            if isinstance(task_type, str):
                return None
            return {t: None for t in task_types}

    def update_delivery_status(self, task_type, content_id, territory, status):
        query = """ 
        UPDATE videocentral.file_delivery fd
        JOIN videocentral.asset_metadata am ON fd.asset_metadata_id = am.id
        SET fd.status = %s,
            fd.updated_at = NOW()
        WHERE am.content_id = %s
            AND fd.territory = %s
            AND fd.task_type = %s
        """
        try:
            logger.info(f"[DB] Starting update_delivery_status with params: task_type={task_type}, content_id={content_id}, territory={territory}, status={status}")
            
            valid, error = validate(db_status=status, task_type=task_type, territory=territory)
            if not valid:
                logger.error(f"[DB] Validation failed with error: {error}")
                return False
            
            logger.info(f"[DB] Validation passed, executing query with params: {status}, {content_id}, {territory}, {task_type}")
            
            # Check if the connection is still alive
            if not self.conn.is_connected():
                logger.error("[DB] Database connection is not active, attempting to reconnect")
                self.conn.reconnect()
            task_type = 'MEC_MOVIE' if task_type == 'MEC_TITLE' else task_type
            # Execute the query
            self.cursor.execute(query, (status, content_id, territory, task_type))
            
            # Check if any rows were affected
            rows_affected = self.cursor.rowcount
            logger.info(f"[DB] Query executed, rows affected: {rows_affected}")
            
            if rows_affected == 0:
                logger.warning(f"[DB] No rows were updated for content_id: {content_id}, territory: {territory}, task_type: {task_type}")
            
            # Commit the transaction
            self.conn.commit()
            logger.info(
                f"[DB] Updated delivery status to {status} for content_id: {content_id}, territory: {territory}, task_type: {task_type}"
            )
            return True
        except Error as e:
            self.conn.rollback()
            logger.error(f"[DB] Error updating delivery status: {e}")
            return False

    def check_gobe_content_image(self, asset_id, content_type_id, image_url):
        query = """
        SELECT c.mezz_file_path
        FROM gobe_catalog.gobe_Asset a
        JOIN gobe_catalog.gobe_Content c ON c.master_asset_id = a.asset_id
            AND c.content_type_id = %s
        WHERE c.mezz_catalog IS NOT NULL
            AND c.mezz_file_path IS NOT NULL
            AND c.is_publishable = 'YES'
            AND a.asset_id = %s
        """

        try:
            result = self.execute(query, (content_type_id, asset_id))

            if result and isinstance(result[0], dict):
                matches = result[0].get("mezz_file_path") == image_url
                return (True, matches)

            return (False, False)

        except Error as e:
            print(f"Error checking gobe content image: {e}")
            return (False, False)

    def update_gobe_content_image(self, asset_id, content_type_id, image_url):
        query = """
        UPDATE gobe_catalog.gobe_Asset a
        JOIN gobe_catalog.gobe_Content c ON c.master_asset_id = a.asset_id
            AND c.content_type_id = %s
        SET c.mezz_file_path = %s
        WHERE c.mezz_catalog IS NOT NULL
            AND c.mezz_file_path IS NOT NULL
            AND c.is_publishable = 'YES'
            AND a.asset_id = %s
        """

        try:
            self.cursor.execute(query, (content_type_id, image_url, asset_id))
            self.conn.commit()
            return True
        except Error as e:
            self.conn.rollback()
            print(f"Error updating gobe content image: {e}")
            return False

    def insert_gobe_content_image(
        self, asset_id, content_type_id, bucket_name, image_url
    ):
        query = """
        INSERT INTO gobe_catalog.gobe_Content (
            content_type_id, mezz_catalog, mezz_file_path, master_asset_id, 
            duration, title, is_publishable, mezz_aspectratio, mezz_format, 
            track_number, creation_time, owner, is_premium, QAComplete, 
            episode_number, popularity
        ) VALUES (
            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s, %s, %s
        )
        """

        values = (
            content_type_id,
            bucket_name,
            image_url,
            asset_id,
            "00:00:00",
            "Content Image",
            "YES",
            "16:9",
            "SD-16:9",
            0,
            "EROS",
            0,
            0,
            0,
            2147483647,
        )

        try:
            self.cursor.execute(query, values)
            self.conn.commit()
            return True
        except Error as e:
            self.conn.rollback()
            print(f"Error inserting content image: {e}")
            return False

    def get_delivery_details(self, asset_ids: List[str]) -> List[Dict]:
        """Get delivery details for specific assets"""
        query = """
        SELECT 
            fd.id,
            am.asset_id,
            am.asset_title,
            fd.territory,
            fd.task_type,
            fd.status,
            fd.message,
            fd.revision_number
        FROM
            videocentral.file_delivery fd
                INNER JOIN
            videocentral.asset_metadata am ON am.id = fd.asset_metadata_id
                INNER JOIN
            gobe_catalog.gobe_ContentTypes ct ON ct.content_type_id = am.content_type_id
        WHERE 
            am.asset_id IN ({})
            AND fd.task_type IN ("MOVIE_IMAGE", "MOVIE_COVER_IMAGE", "MOVIE_HERO_IMAGE", "MOVIE_POSTER_IMAGE", "MEC_TITLE")
            AND (fd.message LIKE '%File recently has been delivered%' OR fd.status = 'FAILED')
            AND DATE(fd.updated_at) = CURDATE()
        ORDER BY 
            am.asset_id, fd.updated_at DESC
        """.format(
            ",".join(["%s"] * len(asset_ids))
        )

        try:
            results = self.execute(query, tuple(asset_ids))
            logger.info(f"[DB] Retrieved delivery details for {len(results)} records")
            return results
        except Error as e:
            logger.error(f"[DB] Error getting delivery details: {e}")
            return []

    def get_blocked_territories(self, content_id: str) -> Optional[List[str]]:
        try:
            query = """
            SELECT asset_blocked_location
            FROM videocentral.asset_metadata
            WHERE content_id = %s
            """
            
            logger.info(f"[DB] Fetching blocked territories for content_id={content_id}")
            
            # Check if the connection is still alive
            if not self.conn.is_connected():
                logger.error("[DB] Database connection is not active, attempting to reconnect")
                self.conn.reconnect()

            # Execute the query
            self.cursor.execute(query, (content_id,))
            result = self.cursor.fetchone()

            if result and result.get("asset_blocked_location") is not None:
                return result.get("asset_blocked_location").split(',')

            logger.info(f"[DB] No blocked territories found for content - {content_id}")
            return None
        except Error as e:
            logger.error(f"[DB] Error getting blocked territories: {e}")
            return None

    def check_asset_metadata(self, asset_id: str, content_id: str) -> bool:
        """Check delivery status for one or more task types
        
        Args:
            asset_id: The asset ID to check
            content_id: The content ID to check
        Returns:
            Returns a boolean stating the presense of asset_metadata in database
        """
        try:
            query = """
                SELECT * FROM videocentral.asset_metadata 
                WHERE content_id = %s
                AND asset_id = %s;
            """

            self.cursor.execute(query, (content_id, asset_id,))
            result = self.cursor.fetchone()
            
            if result:
                logger.info(f"🟢 Found asset_metadata with id: {result['id']} and asset_title: {result['asset_title']}")
                return True
            
            logger.info(f"🔴 Failed to find asset_metadata in database for A{asset_id}_C{content_id}")
            return False

        except Exception as e:
            logger.error(f"[DB] Error checking delivery status: {str(e)}")
            return False
        
    def get_available_images(self, asset_id: str) -> Optional[List[str]]:
        try:
            query = """
                SELECT c.content_type_id
                FROM gobe_catalog.gobe_Asset a
                    JOIN gobe_catalog.gobe_Content c ON c.master_asset_id = a.asset_id
                        AND c.content_type_id IN (40, 53, 96, 120)
                WHERE c.mezz_catalog IS NOT NULL
                    AND c.mezz_file_path IS NOT NULL
                    AND c.is_publishable = 'YES'
                    AND a.asset_id = %s
            """
            
            self.cursor.execute(query, (asset_id,))
            results = self.cursor.fetchall()

            if results:
                available_type_ids = [str(row["content_type_id"]) for row in results if "content_type_id" in row]
                # Invert the CONTENT_TYPE_IDS dict to map id -> name
                id_to_type = {str(v): k for k, v in TASK_TYPE_IDS.items()}
                available_types = [id_to_type[id] for id in available_type_ids if id in id_to_type]
                return available_types

            return None
        except Exception as e:
            logger.error(f"🔴 [DB] Error getting available images: {str(e)}")
            return False
    
    def get_asset_details(self, asset_id: str) -> list:
        """Get asset details from database based on title"""
        query = """
            SELECT 
                asset_title,
                content_id,
                is_asset_publishable,
                is_content_publishable
            FROM videocentral.asset_metadata
            WHERE asset_id = %s;
        """
        
        try:
            self.cursor.execute(query, (asset_id,))
            results = self.cursor.fetchall()
            logger.info(f"Results fetched from database for asset {asset_id} ~ {results}")
            return results
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            return []
    
    def close(self):
        if hasattr(self, "cursor") and self.cursor:
            self.cursor.close()
        if hasattr(self, "conn") and self.conn:
            self.conn.close()

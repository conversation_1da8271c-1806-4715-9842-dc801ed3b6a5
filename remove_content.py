import os
import time
import logging
import tempfile
import argparse
import pandas as pd
from datetime import datetime

from db import Database
from spreadsheet_reader import Spreadshe<PERSON><PERSON>eader
from delivery_service import DeliveryService
from config import CONTENT_TYPE_IDS, TERRITORIES

os.makedirs('content_removal', exist_ok=True)

default_source_file = 'EN_CONTENT_REMOVAL.xlsx'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'content_removal/{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RemoveContent:
    def __init__(self, excel_file: str = default_source_file, sheet_name: str = None):
        self.db = Database()
        self.spreadsheet = SpreadsheetReader(excel_file, sheet_name)
        self.api = DeliveryService()
        self.temp_dir = tempfile.mkdtemp()
        self.excel_file = excel_file
        self.stats = {
            'total_assets': 0,
            'total_territories': 3,
            'effective_total': 0,
            'total_success': 0,
            'total_failed': 0,
            'total_skipped': 0,
            'failed_assets': [],
            'skipped_assets': [],
            'start_time': None,
            'end_time': None
        }
        
    def run(self):
        self.stats['start_time'] = datetime.now()
        df = self.spreadsheet.read_excel_for_removal()
        self.stats['total_assets'] = len(df)
        
        # Process each row for all territories
        for index, row in df.iterrows():
            asset_id = row.get('asset_id')
            content_id = row.get('content_id')

            if (asset_id is None or asset_id == '' or str(asset_id).lower() in ['nan', 'none', 'null'] or not str(asset_id).strip() or
                content_id is None or content_id == '' or str(content_id).lower() in ['nan', 'none', 'null'] or not str(content_id).strip()):
                logger.warning(f"⏭️ Skipping title '{row.get('titles', 'Unknown')}' at row {index}: Missing asset_id or content_id")
                self.stats['total_skipped'] += 1
                self.stats['skipped_assets'].append({
                    "index": index,
                    "title": row.get('titles', 'Unknown'),
                    "asset_id": asset_id,
                    "content_id": content_id,
                    "error": "Missing asset_id or content_id"
                })
                continue
            
            blocked_territories = self.db.get_blocked_territories(row['content_id'])
            territories = list(set(TERRITORIES) - set(blocked_territories or []))
            logger.info(f"🤜 Removed blocked territories from list: {blocked_territories}")
            
            if blocked_territories is not None and 'WW' in blocked_territories:
                logger.info(f"⏭️ Skipping title '{row.get('titles', 'Unknown')}' at row {index}: Content blocked World Wide")
                self.stats['total_skipped'] += 1
                self.stats['skipped_assets'].append({
                    "index": index,
                    "title": row.get('titles', 'Unknown'),
                    "asset_id": asset_id,
                    "content_id": content_id,
                    "error": "Blocked WW"
                })
                continue
                    
            for territory in territories:
                if not self.api.initiate_redelivery(row['content_id'], row['asset_id'], territory, 'AVAILS', '2025-07-09'):
                    logger.info(f"❌ Failed process redelivery for {row['titles']} in {territory}")
                    self.stats['failed_assets'].append({
                        "index": index,
                        "title": row.get('titles', 'Unknown'),
                        "asset_id": asset_id,
                        "content_id": content_id,
                        "territory": territory,
                        "error": "Failed to initiate redelivery"
                    })
                    self.stats['total_failed'] += 1
                else:
                    logger.info(f"✅ Successfully completed redelivery for {row['titles']} in {territory}")
                    self.stats['total_success'] += 1
                    
    def _print_summary(self):
        """Print summary statistics and delivery details"""
        self.stats['end_time'] = datetime.now()
        duration = self.stats['end_time'] - self.stats['start_time']
        
        # Export failed rows to Excel if there are any failures
        if self.stats['failed_assets'] or self.stats['skipped_assets']:
            self._export_failed_rows_to_excel()
        
        logger.info("\n\n")
        logger.info("=== Processing Summary ===")
        logger.info(f"Total Assets: {self.stats['total_assets']}")
        logger.info(f"Total Success: {self.stats['total_success']}")
        logger.info(f"Total Failed: {self.stats['total_failed']}")
        logger.info(f"Total Skipped: {self.stats['total_skipped']}")

        if self.stats['skipped_assets']:
            logger.info("\n--- Skipped Assets ---")
            for asset in self.stats['skipped_assets']:
                logger.info(f"Row: {asset['index']}")
                logger.info(f"Title: {asset['title']}")
                logger.info(f"Asset ID: {asset['asset_id']}")
                logger.info(f"Content ID: {asset['content_id']}")
                logger.info(f"Error: {asset['error']}")
                logger.info("---")

        if self.stats['failed_assets']:
            logger.info("\n--- Failed Assets ---")
            for asset in self.stats['failed_assets']:
                logger.info(f"Row: {asset['index']}")
                logger.info(f"Title: {asset['title']}")
                logger.info(f"Territory: {asset['territory']}")
                logger.info(f"Asset ID: {asset['asset_id']}")
                logger.info(f"Content ID: {asset['content_id']}")
                logger.info(f"Error: {asset['error']}")
                logger.info("---")
            
        logger.info("\n\n")
        logger.info("==============================")
        logger.info(f"Total Duration: {duration}")
        logger.info("==============================\n\n")

    def _export_failed_rows_to_excel(self):
        """Export failed and skipped rows to a new Excel file"""
        try:
            # Format date as DDMonth (e.g., 14July)
            current_date = datetime.now()
            formatted_date = current_date.strftime("%d%B")  # e.g., 14July
            output_file = f"failed_removals_{formatted_date}.xlsx"

            # Read the original data to get the full row information
            df_original = self.spreadsheet.read_excel_for_removal()

            failed_data = []
            skipped_data = []

            # Process failed assets
            for asset in self.stats['failed_assets']:
                row_index = asset['index']
                if row_index < len(df_original):
                    row_data = df_original.iloc[row_index].to_dict()
                    row_data['failure_reason'] = asset['error']
                    row_data['territory'] = asset['territory']
                    row_data['status'] = 'FAILED'
                    failed_data.append(row_data)

            # Process skipped assets
            for asset in self.stats['skipped_assets']:
                row_index = asset['index']
                if row_index < len(df_original):
                    row_data = df_original.iloc[row_index].to_dict()
                    row_data['failure_reason'] = asset['error']
                    row_data['territory'] = 'N/A'
                    row_data['status'] = 'SKIPPED'
                    skipped_data.append(row_data)

            # Create DataFrames
            all_failed_data = failed_data + skipped_data

            if all_failed_data:
                df_failed = pd.DataFrame(all_failed_data)

                # Write to Excel with multiple sheets
                with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                    # All failed/skipped rows
                    df_failed.to_excel(writer, sheet_name='All_Failed_Rows', index=False)

                    # Separate sheets for failed and skipped if both exist
                    if failed_data:
                        df_failed_only = pd.DataFrame(failed_data)
                        df_failed_only.to_excel(writer, sheet_name='Failed_Deliveries', index=False)

                    if skipped_data:
                        df_skipped_only = pd.DataFrame(skipped_data)
                        df_skipped_only.to_excel(writer, sheet_name='Skipped_Rows', index=False)

                logger.info(f"📊 Failed rows exported to: {output_file}")
                logger.info(f"   - Total failed/skipped rows: {len(all_failed_data)}")
                logger.info(f"   - Failed deliveries: {len(failed_data)}")
                logger.info(f"   - Skipped rows: {len(skipped_data)}")
            else:
                logger.info("📊 No failed or skipped rows to export")

        except Exception as e:
            logger.error(f"❌ Error exporting failed rows to Excel: {str(e)}")

    def _cleanup(self):
        """Clean up temporary files and close connections"""
        try:
            self.db.close()
        except Exception as e:
            logger.warning(f"Error during cleanup: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description='Process asset deliveries from Excel file')
    parser.add_argument('-f', '--file', 
                        default=default_source_file,
                        help=f'Excel file to process (default: images.xlsx)')
    parser.add_argument('-s', '--sheet',
                        default="Sheet1",
                        help='Specify sheet name if using a different sheet')
    args = parser.parse_args()

    remover = RemoveContent(args.file, sheet_name=args.sheet)
    try:
        remover.run()
        remover._print_summary()
    finally:
        remover._cleanup()

if __name__ == "__main__":
    main() 
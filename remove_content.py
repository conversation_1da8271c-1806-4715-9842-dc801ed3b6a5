import os
import time
import logging
import tempfile
import argparse
from datetime import datetime

from db import Database
from spreadsheet_reader import Sp<PERSON>she<PERSON><PERSON>eader
from delivery_service import DeliveryService
from config import CONTENT_TYPE_IDS, TERRITORIES

os.makedirs('content_removal', exist_ok=True)

default_source_file = 'EN_CONTENT_REMOVAL.xlsx'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'logs/asset_delivery_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class RemoveContent:
    def __init__(self, excel_file: str = default_source_file, sheet_name: str = None):
        self.db = Database()
        self.spreadsheet = SpreadsheetReader(excel_file, sheet_name)
        self.api = DeliveryService()
        self.temp_dir = tempfile.mkdtemp()
        self.excel_file = excel_file
        self.stats = {
            'total_assets': 0,
            'total_success': 0,
            'total_failed': 0,
            'failed_assets': [],
            'start_time': None,
            'end_time': None
        }
        
    def run(self):
        df = self.spreadsheet.read_excel_for_removal()
        self.stats['total_assets'] = len(df)
        
        # Process each row for all territories
        for index, row in df.iterrows():
            for territory in TERRITORIES:
                if not self.api.initiate_redelivery(row['content_id'], row['asset_id'], territory, 'AVAILS', '2025-07-09'):
                    logger.info(f"❌ Failed to initiate redelivery for {row['titles']} {territory}")
                    self.stats['failed_assets'].append({
                        "index": index,
                        "asset_id": row['asset_id'],
                        "content_id": row['content_id'],
                        "territory": territory,
                        "error": "Failed to initiate redelivery"
                    })
                    self.stats['total_failed'] += 1
                else:
                    self.stats['total_success'] += 1
                    
                logger.info(f"ℹ️ Processed {territory} for asset {row['asset_id']}")
                logger.info(f"🕑 Waiting for 5 seconds before delivering next asset...")
                time.sleep(5)
        
    def _print_summary(self):
        """Print summary statistics and delivery details"""
        self.stats['end_time'] = datetime.now()
        duration = self.stats['end_time'] - self.stats['start_time']
        
        logger.info("\n\n")
        logger.info("=== Processing Summary ===")
        logger.info(f"Total Assets: {self.stats['total_assets']}")    
        logger.info(f"Total Success: {self.stats['total_success']}")
        logger.info(f"Total Failed: {self.stats['total_failed']}")
        for asset in self.stats['failed_assets']:
            logger.info(f"Row: {asset.index}")
            logger.info(f"Territory: {asset.territory}")
            logger.info(f"Asset ID: {asset.asset_id}")
            logger.info(f"Content ID: {asset.content_id}")
            logger.info(f"Error: {asset.error}")
            
        logger.info("\n\n")
        logger.info("==============================")
        logger.info(f"Total Duration: {duration}")
        logger.info("==============================\n\n")

    def _cleanup(self):
        """Clean up temporary files and close connections"""
        try:
            self.db.close()
            
        except Exception as e:
            logger.warning(f"Error during cleanup: {str(e)}")
        
def main():
    parser = argparse.ArgumentParser(description='Process asset deliveries from Excel file')
    parser.add_argument('-f', '--file', 
                        default=default_source_file,
                        help=f'Excel file to process (default: images.xlsx)')
    parser.add_argument('-s', '--sheet',
                        default="Sheet1",
                        help='Specify sheet name if using a different sheet')
    args = parser.parse_args()

    remover = RemoveContent(args.file, sheet_name=args.sheet)

if __name__ == "__main__":
    main() 
import pandas as pd
import logging
from datetime import datetime
import os
import argparse
import time
from db import Database

# Create logs directory if it doesn't exist
os.makedirs('logs', exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'/validate_redelivery_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def get_delivery_details(asset_ids: list, titles: list) -> list:
    """Get delivery details for specific assets"""
    # Filter out empty strings and create a list of non-empty asset IDs
    non_empty_asset_ids = [aid for aid in asset_ids if aid and aid != '']
    
    # Base query without the IN clause
    base_query = """
    SELECT 
        fd.id,
        am.asset_id,
        am.asset_title,
        fd.territory,
        fd.task_type,
        fd.status,
        fd.message,
        fd.revision_number,
        am.content_id
    FROM
        videocentral.file_delivery fd
            INNER JOIN
        videocentral.asset_metadata am ON am.id = fd.asset_metadata_id
            INNER JOIN
        gobe_catalog.gobe_ContentTypes ct ON ct.content_type_id = am.content_type_id
    WHERE 
        fd.task_type IN ("AVAILS")
        AND (
            fd.status = 'FAILED' 
            OR am.asset_id = ''
            OR am.content_id = ''
            OR fd.status IS NULL
            OR am.asset_id IS NULL 
            OR am.content_id IS NULL
            OR fd.message LIKE '%File recently has been delivered%'
        )
    """
    
    # Add IN clause only if we have non-empty asset IDs
    if non_empty_asset_ids:
        base_query += " AND am.asset_id IN ({})".format(','.join(['%s'] * len(non_empty_asset_ids)))
    
    base_query += " ORDER BY am.asset_id, fd.updated_at DESC"
    
    try:
        db = Database()
        if non_empty_asset_ids:
            results = db.execute(base_query, tuple(non_empty_asset_ids))
        else:
            results = db.execute(base_query)
        db.close()

        # Create a mapping of asset_id to title from Excel
        title_mapping = dict(zip(asset_ids, titles))
        
        # Add Excel titles to results
        for result in results:
            result['excel_title'] = title_mapping.get(result['asset_id'], '')
        
        logger.info(f"Retrieved delivery details for {len(results)} records")
        return results
    except Exception as e:
        logger.error(f"Error getting delivery details: {e}")
        return []

def process_excel_file(excel_file: str, wait_time: int = 0):
    """Process Excel file and check delivery status"""
    try:
        # Read Excel file
        if not os.path.exists(excel_file):
            logger.error(f"Excel file not found: {excel_file}")
            return

        df = pd.read_excel(excel_file)
        logger.info(f"Loaded Excel file: {excel_file}")

        # Get list of asset IDs and titles, including empty values
        asset_ids = df['asset_id'].fillna('').astype(str).tolist()
        titles = df['Title'].fillna('').astype(str).tolist()
        
        if len(asset_ids) == 0:
            logger.error("Excel file is empty")
            return

        # Wait if specified
        if wait_time > 0:
            logger.info(f"Waiting {wait_time} seconds for requests to settle...")
            time.sleep(wait_time)

        # Get delivery details from database
        delivery_details = get_delivery_details(asset_ids, titles)
        
        # Create a set of asset_ids that have database entries
        db_asset_ids = {str(detail['asset_id']) for detail in delivery_details if detail['asset_id']}
        
        # Add entries from Excel that have empty asset_id and content_id
        for idx, (asset_id, title) in enumerate(zip(asset_ids, titles)):
            if (not asset_id or asset_id == '') and (not asset_id in db_asset_ids):
                delivery_details.append({
                    'id': f'EXCEL_{idx}',
                    'asset_id': '',
                    'content_id': '',
                    'asset_title': '',
                    'excel_title': title,
                    'territory': '',
                    'task_type': 'MISSING_IDS',
                    'status': 'PENDING',
                    'revision_number': '',
                    'message': 'Missing asset_id and content_id in Excel'
                })
        
        if delivery_details:
            # Print header
            logger.info("\n=== Delivery Details ===")
            logger.info(f"{'ID':<8} {'Asset ID':<10} {'Content ID':<10} {'Excel Title':<40} {'Asset Title':<40} {'Territory':<6} {'Task Type':<20} {'Status':<10} {'Revision':<10}")
            logger.info("-" * 160)
            
            # Print rows
            for detail in delivery_details:
                asset_id = str(detail['asset_id']) if detail['asset_id'] else ''
                content_id = str(detail['content_id']) if detail['content_id'] else ''
                excel_title = str(detail['excel_title'])[:40] if detail['excel_title'] else ''
                asset_title = str(detail['asset_title'])[:40] if detail['asset_title'] else ''
                
                logger.info(
                    f"{str(detail['id']):<8} "
                    f"{asset_id:<10} "
                    f"{content_id:<10} "
                    f"{excel_title:<40} "
                    f"{asset_title:<40} "
                    f"{str(detail['territory']):<6} "
                    f"{str(detail['task_type']):<20} "
                    f"{str(detail['status']):<10} "
                    f"{str(detail['revision_number']):<10}"
                )
        else:
            logger.info("No delivery details found for the assets.")
        logger.info("========================")

    except Exception as e:
        logger.error(f"Error processing Excel file: {str(e)}")

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description='Validate delivery status for assets in Excel file')
    parser.add_argument('-f', '--file', default='redelivery_list.xlsx',
                      help='Excel file to process (default: redelivery_list.xlsx)')
    parser.add_argument('-w', '--wait', type=int, default=0,
                      help='Wait time in seconds before checking status (default: 0)')
    
    args = parser.parse_args()
    
    logger.info(f"Starting validation process with file: {args.file}")
    process_excel_file(args.file, args.wait)
    logger.info("Validation process completed")

if __name__ == "__main__":
    main()

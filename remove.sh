#!/bin/bash

# Configuration
FILE_NAME="EN_CONTENT_REMOVAL.xlsx"
# SHEET_NAME="Validation_Results"
OUTPUT_FILE="EN_CONTENT_REMOVAL.xlsx"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=== Content Removal Pipeline ===${NC}"
echo -e "${BLUE}Input file: ${FILE_NAME}${NC}"
echo -e "${BLUE}Output file: ${OUTPUT_FILE}${NC}"
echo ""

# Check if input file exists
if [ ! -f "$FILE_NAME" ]; then
    echo -e "${RED}Error: Input file '$FILE_NAME' not found!${NC}"
    echo -e "${YELLOW}Please update the FILE_NAME variable in this script or place the file in the current directory.${NC}"
    exit 1
fi

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo -e "${RED}Error: Virtual environment 'venv' not found!${NC}"
    echo -e "${YELLOW}Please create a virtual environment first:${NC}"
    echo -e "${YELLOW}  python -m venv venv${NC}"
    exit 1
fi

# Activate virtual environment
echo -e "${YELLOW}Activating virtual environment...${NC}"
source venv/bin/activate

# Check if activation was successful
if [ -z "$VIRTUAL_ENV" ]; then
    echo -e "${RED}Error: Failed to activate virtual environment!${NC}"
    exit 1
fi

echo -e "${GREEN}Virtual environment activated: $VIRTUAL_ENV${NC}"
echo ""

# Step 1: Fill data from title
echo -e "${BLUE}Step 1: Processing file through fill_data_from_title.py...${NC}"
./venv/bin/python fill_data_from_title.py -f "$FILE_NAME"

# Check if the command was successful
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: fill_data_from_title.py failed!${NC}"
    deactivate
    exit 1
fi

echo -e "${GREEN}Step 1 completed successfully!${NC}"
echo ""

# Step 2: Rename the input file to the expected output name
echo -e "${BLUE}Step 2: Renaming ${FILE_NAME} to ${OUTPUT_FILE}...${NC}"

# Check if the input file still exists (it should have been modified in place)
if [ ! -f "$FILE_NAME" ]; then
    echo -e "${RED}Error: Input file '$FILE_NAME' not found after processing!${NC}"
    deactivate
    exit 1
fi

# Rename the input file to the expected output name
mv "$FILE_NAME" "$OUTPUT_FILE"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}Successfully renamed '$FILE_NAME' to '$OUTPUT_FILE'${NC}"
else
    echo -e "${RED}Error: Failed to rename '$FILE_NAME' to '$OUTPUT_FILE'${NC}"
    deactivate
    exit 1
fi

echo ""

# Step 3: Run content removal
echo -e "${BLUE}Step 3: Running content removal process...${NC}"
./venv/bin/python remove_content.py

# Check if the command was successful
if [ $? -ne 0 ]; then
    echo -e "${RED}Error: remove_content.py failed!${NC}"
    deactivate
    exit 1
fi

echo -e "${GREEN}Step 3 completed successfully!${NC}"
echo ""

# Deactivate virtual environment
echo -e "${YELLOW}Deactivating virtual environment...${NC}"
deactivate

echo -e "${GREEN}=== Pipeline completed successfully! ===${NC}"
echo -e "${BLUE}Processed file: ${FILE_NAME}${NC}"
echo -e "${BLUE}Final output: Check logs for removal results${NC}"
